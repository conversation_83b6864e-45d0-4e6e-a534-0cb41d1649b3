"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/sections/PilotCampaign.tsx":
/*!****************************************!*\
  !*** ./app/sections/PilotCampaign.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ScaleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BanknotesIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,HomeIcon,PencilIcon,ScaleIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst PilotCampaign = ()=>{\n    const campaignDetails = [\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"12 Week Duration\",\n            description: \"Extended pilot to measure deep market impact and ROI\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"7 Posts Per Week\",\n            description: \"High-quality content across TikTok, Instagram & LinkedIn\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"±2M IDR/Week\",\n            description: \"Professional editor + enhanced posting budget\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Advanced Analytics\",\n            description: \"Google Data Studio + TikTok Analytics + IG Insights\"\n        }\n    ];\n    const contentPillars = [\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Legal & Process\",\n            topics: [\n                \"Leasehold vs. freehold\",\n                \"Transfer requirements\",\n                \"Legal fees\",\n                \"Taxes\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Material & Build Quality\",\n            topics: [\n                \"Timber vs. concrete pros/cons\",\n                \"Roofing options\",\n                \"Energy-proofing\",\n                \"Finishes\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Cost Breakdown\",\n            topics: [\n                \"Build cost per m²\",\n                \"Hidden costs\",\n                \"Infrastructure\",\n                \"Permits\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Tenant & ROI\",\n            topics: [\n                \"Rental yield vs. capital growth\",\n                \"Case examples\",\n                \"Short vs. long-term rentals\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Investor Profiles\",\n            topics: [\n                \"Private expats & families\",\n                \"Small investor funds\",\n                \"Minimum investment\",\n                \"Expected returns\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BanknotesIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_HomeIcon_PencilIcon_ScaleIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Local Trust Network\",\n            topics: [\n                \"Vetted architects & lawyers\",\n                \"Trusted funders\",\n                \"Builder verification\",\n                \"FAQs\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.span, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6\",\n                            children: \"Content Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-4xl md:text-5xl font-bold text-default mb-6 leading-tight\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"12-Week\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Content Strategy\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-xl text-muted max-w-3xl mx-auto\",\n                            children: \"Deep content pillars and multi-platform approach to establish market authority and build trust with potential investors.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-default text-center mb-8\",\n                            children: \"Campaign Structure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: campaignDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1 * index\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(detail.icon, {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-default mb-2\",\n                                            children: detail.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted\",\n                                            children: detail.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-default text-center mb-12\",\n                            children: \"Content Strategy & Timeline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-8 xl:gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"order-2 lg:order-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20 h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-default mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-2\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Content Pillars (Deep & Broad)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: contentPillars.map((pillar, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.4,\n                                                            delay: 0.4 + index * 0.1\n                                                        },\n                                                        className: \"border-l-4 border-primary pl-4 py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(pillar.icon, {\n                                                                        className: \"w-4 h-4 text-primary mr-2 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-semibold text-default text-sm\",\n                                                                        children: pillar.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: pillar.topics.map((topic, topicIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-white/70 text-muted px-2 py-1 rounded-full border border-gray-200\",\n                                                                        children: topic\n                                                                    }, topicIndex, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"order-1 lg:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-3 mb-6 justify-center lg:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: \"Instagram\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center bg-black text-white px-4 py-2 rounded-lg shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 bg-black rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: \"TikTok\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.95\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.4\n                                            },\n                                            className: \"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden max-w-sm mx-auto lg:mx-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-primary to-accent h-40 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white px-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-bold mb-2\",\n                                                                children: \"Build Cost Breakdown\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-90\",\n                                                                children: \"Villa construction: What €200k really gets you\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: \"paradise_indonesia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted leading-relaxed\",\n                                                            children: [\n                                                                \"Breaking down real construction costs per m\\xb2 in Bali. From timber vs concrete to hidden infrastructure costs...\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary font-medium\",\n                                                                    children: \"#BaliProperty #BuildCosts #Investment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza presentation\\\\Property-plaza-preza\\\\app\\\\sections\\\\PilotCampaign.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PilotCampaign;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PilotCampaign);\nvar _c;\n$RefreshReg$(_c, \"PilotCampaign\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/sections/PilotCampaign.tsx\n"));

/***/ })

});