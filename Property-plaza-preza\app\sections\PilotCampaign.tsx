"use client";

import { motion } from 'framer-motion';
import { CalendarIcon, PencilIcon, CurrencyDollarIcon, ChartBarIcon, ScaleIcon, HomeIcon, CogIcon, ArrowTrendingUpIcon, UsersIcon, ShieldCheckIcon, BuildingOfficeIcon, BanknotesIcon } from '@heroicons/react/24/outline';

const PilotCampaign = () => {
  const campaignDetails = [
    {
      icon: CalendarIcon,
      title: "12 Week Duration",
      description: "Extended pilot to measure deep market impact and ROI"
    },
    {
      icon: PencilIcon,
      title: "7 Posts Per Week",
      description: "High-quality content across TikTok, Instagram & LinkedIn"
    },
    {
      icon: CurrencyDollarIcon,
      title: "±2M IDR/Week",
      description: "Professional editor + enhanced posting budget"
    },
    {
      icon: ChartBarIcon,
      title: "Advanced Analytics",
      description: "Google Data Studio + TikTok Analytics + IG Insights"
    }
  ];

  const contentPillars = [
    {
      icon: ScaleIcon,
      title: "Legal & Process",
      topics: ["Leasehold vs. freehold", "Transfer requirements", "Legal fees", "Taxes"]
    },
    {
      icon: HomeIcon,
      title: "Material & Build Quality",
      topics: ["Timber vs. concrete pros/cons", "Roofing options", "Energy-proofing", "Finishes"]
    },
    {
      icon: BanknotesIcon,
      title: "Cost Breakdown",
      topics: ["Build cost per m²", "Hidden costs", "Infrastructure", "Permits"]
    },
    {
      icon: ArrowTrendingUpIcon,
      title: "Tenant & ROI",
      topics: ["Rental yield vs. capital growth", "Case examples", "Short vs. long-term rentals"]
    },
    {
      icon: UsersIcon,
      title: "Investor Profiles",
      topics: ["Private expats & families", "Small investor funds", "Minimum investment", "Expected returns"]
    },
    {
      icon: ShieldCheckIcon,
      title: "Local Trust Network",
      topics: ["Vetted architects & lawyers", "Trusted funders", "Builder verification", "FAQs"]
    }
  ];

  return (
    <section className="snap-section min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-background py-20">
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-6"
          >
            Content Strategy
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-default mb-6 leading-tight"
          >
            <span className="gradient-text">12-Week</span> Content Strategy
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted max-w-3xl mx-auto"
          >
            Deep content pillars and multi-platform approach to establish market authority and build trust with potential investors.
          </motion.p>
        </motion.div>

        {/* Campaign Structure - Horizontal Layout */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-default text-center mb-8">Campaign Structure</h3>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {campaignDetails.map((detail, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow text-center"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <detail.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-default mb-2">{detail.title}</h4>
                <p className="text-sm text-muted">{detail.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Content Strategy Section - Full Width */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-default text-center mb-12">Content Strategy & Timeline</h3>

          <div className="grid lg:grid-cols-2 gap-8 xl:gap-12">
            {/* Left Side - Content Pillars */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="order-2 lg:order-1"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20 h-full"
              >
                <h4 className="text-lg font-bold text-default mb-6 flex items-center">
                  <span className="text-2xl mr-2">💡</span>
                  Content Pillars (Deep & Broad)
                </h4>
                <div className="space-y-4">
                  {contentPillars.map((pillar, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                      className="border-l-4 border-primary pl-4 py-2"
                    >
                      <div className="flex items-center mb-2">
                        <pillar.icon className="w-4 h-4 text-primary mr-2 flex-shrink-0" />
                        <h5 className="font-semibold text-default text-sm">{pillar.title}</h5>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {pillar.topics.map((topic, topicIndex) => (
                          <span
                            key={topicIndex}
                            className="text-xs bg-white/70 text-muted px-2 py-1 rounded-full border border-gray-200"
                          >
                            {topic}
                          </span>
                        ))}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>

            {/* Right Side - Social Media Preview */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="order-1 lg:order-2"
            >
              {/* Platform Tabs */}
              <div className="flex flex-wrap gap-3 mb-6 justify-center lg:justify-start">
                <div className="flex items-center bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg shadow-lg">
                  <div className="w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center">
                    <div className="w-3 h-3 bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500 rounded-sm"></div>
                  </div>
                  <span className="text-sm font-semibold">Instagram</span>
                </div>
                <div className="flex items-center bg-black text-white px-4 py-2 rounded-lg shadow-lg">
                  <div className="w-5 h-5 mr-2 bg-white rounded-sm flex items-center justify-center">
                    <div className="w-3 h-3 bg-black rounded-full"></div>
                  </div>
                  <span className="text-sm font-semibold">TikTok</span>
                </div>
              </div>

              {/* Content Preview */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden max-w-sm mx-auto lg:mx-0"
              >
                <div className="bg-gradient-to-r from-primary to-accent h-40 flex items-center justify-center">
                  <div className="text-center text-white px-4">
                    <h4 className="text-lg font-bold mb-2">Build Cost Breakdown</h4>
                    <p className="text-sm opacity-90">Villa construction: What €200k really gets you</p>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full mr-3 flex-shrink-0"></div>
                    <span className="font-semibold text-sm">paradise_indonesia</span>
                  </div>
                  <p className="text-sm text-muted leading-relaxed">
                    Breaking down real construction costs per m² in Bali. From timber vs concrete to hidden infrastructure costs...
                    <br />
                    <span className="text-primary font-medium">#BaliProperty #BuildCosts #Investment</span>
                  </p>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PilotCampaign;
